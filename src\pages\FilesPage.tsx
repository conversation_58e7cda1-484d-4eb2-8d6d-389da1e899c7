import React, { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'
import { useAppStore } from '../store'
import { FileTreeNode } from '../types'
import { vaultUIManager } from '../services/vaultUIManager'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faFolder,
  faFolderOpen,
  faFileText,
  faFileCode,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileImage,
  faFileVideo,
  faFileAudio,
  faFileZipper,
  faFile,
  faChevronDown,
  faChevronRight,
  faFolderTree,
  faPlus,
  faSitemap,
  faLightbulb,
  faClock,
  faUser,
  faInfoCircle,
  faStar,
  faCheck,
  faRocket,
  faTerminal,
  faCode,
  faPuzzlePiece,
  faCube,
  faCubes,
  faArrowLeft,
  faArrowRight,
  faHome,
  faEye,
  faTh,
  faList,
  faEllipsisVertical,
  faHdd,
  faArrowRotateRight,
  faSort,
  faSortUp,
  faSortDown,
  faThLarge,
  faFileLines
} from '@fortawesome/free-solid-svg-icons'

// FileTreeNode interface is now imported from types

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}

// Helper function to get file type icon and color
const getFileTypeIcon = (fileName: string, fileType: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase()

  if (fileType === 'Folder') {
    return { icon: faFolder, color: 'text-supplement2', bgColor: 'bg-supplement2/20' }
  }

  switch (extension) {
    case 'md':
    case 'markdown':
      return { icon: faFileText, color: 'text-primary', bgColor: 'bg-primary/20' }
    case 'json':
      return { icon: faFileCode, color: 'text-yellow-400', bgColor: 'bg-yellow-500/20' }
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return { icon: faFileCode, color: 'text-blue-400', bgColor: 'bg-blue-500/20' }
    case 'pdf':
      return { icon: faFilePdf, color: 'text-red-400', bgColor: 'bg-red-500/20' }
    case 'doc':
    case 'docx':
      return { icon: faFileWord, color: 'text-blue-600', bgColor: 'bg-blue-600/20' }
    case 'xls':
    case 'xlsx':
      return { icon: faFileExcel, color: 'text-green-400', bgColor: 'bg-green-500/20' }
    case 'ppt':
    case 'pptx':
      return { icon: faFilePowerpoint, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
      return { icon: faFileImage, color: 'text-purple-400', bgColor: 'bg-purple-500/20' }
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return { icon: faFileVideo, color: 'text-pink-400', bgColor: 'bg-pink-500/20' }
    case 'mp3':
    case 'wav':
    case 'flac':
      return { icon: faFileAudio, color: 'text-indigo-400', bgColor: 'bg-indigo-500/20' }
    case 'zip':
    case 'rar':
    case '7z':
      return { icon: faFileZipper, color: 'text-gray-400', bgColor: 'bg-gray-500/20' }
    case 'css':
      return { icon: faFileCode, color: 'text-cyan-400', bgColor: 'bg-cyan-500/20' }
    case 'html':
    case 'htm':
      return { icon: faFileCode, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    default:
      return { icon: faFile, color: 'text-supplement1', bgColor: 'bg-supplement1/20' }
  }
}

const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const [searchParams] = useSearchParams()
  const { artifactsVisible } = useAppStore()

  // Get context from URL params
  const contextFromUrl = searchParams.get('context') || contextId

  // State for vault data
  const [fileTree, setFileTree] = useState<FileTreeNode[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState<string | null>(null)

  // State for UI
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'explorer', // Start with explorer, will switch to master when master.md is found
    showArtifacts: false,
    artifactsExpanded: false
  })

  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [masterContent, setMasterContent] = useState<string>('')
  const [masterLoading, setMasterLoading] = useState(false)

  // Load vault file tree on mount
  useEffect(() => {
    console.log('FilesPage mounted, loading file tree...')
    loadFileTree()
  }, []) // Run once on mount

  // Reload when context changes
  useEffect(() => {
    if (contextFromUrl) {
      console.log('Context changed to:', contextFromUrl, 'reloading file tree...')
      loadFileTree()
    }
  }, [contextFromUrl])

  // Debug: Log when fileTree changes
  useEffect(() => {
    console.log('File tree updated:', fileTree.length, 'items')
    console.log('Expanded folders:', Array.from(expandedFolders))
    console.log('Selected file:', selectedFile)
    console.log('Current view mode:', viewMode.currentMode)
  }, [fileTree, expandedFolders, selectedFile, viewMode])

  const loadFileTree = async () => {
    try {
      setLoading(true)
      setError(null)

      const tree = await vaultUIManager.getFileTree(contextFromUrl || undefined)
      console.log('Raw file tree from vaultUIManager:', tree)
      setFileTree(tree)

      // Auto-expand and select first master.md (like GitHub README)
      if (tree && tree.length > 0) {
        console.log('Processing file tree with', tree.length, 'root nodes')
        const expandedPaths = new Set<string>()
        let firstMasterPath: string | null = null

        // Function to recursively find master.md and expand folders
        const findMasterAndExpand = (nodes: FileTreeNode[], parentPath: string = '') => {
          console.log('Processing nodes at level:', parentPath, 'nodes:', nodes.length)
          for (const node of nodes) {
            console.log('Processing node:', node.name, 'type:', node.type, 'path:', node.path)
            if (node.type === 'folder') {
              // Auto-expand all vault and context folders
              expandedPaths.add(node.path)
              console.log('Added to expanded paths:', node.path)

              if (node.children) {
                findMasterAndExpand(node.children, node.path)
              }
            } else if (node.type === 'file' && node.name === 'master.md' && !firstMasterPath) {
              // Found the first master.md
              firstMasterPath = node.path
              console.log('Found first master.md at:', firstMasterPath)
            } else if (node.type === 'file') {
              console.log('Found file:', node.name, 'at:', node.path)
            }
          }
        }

        findMasterAndExpand(tree)

        // Update state with expanded folders and selected master.md
        console.log('Setting expanded folders:', Array.from(expandedPaths))
        setExpandedFolders(expandedPaths)

        if (firstMasterPath) {
          console.log('Setting selected file and switching to master mode:', firstMasterPath)
          setSelectedFile(firstMasterPath)

          // Use setTimeout to ensure state updates are processed
          setTimeout(() => {
            setViewMode(prev => {
              console.log('Switching view mode from', prev.currentMode, 'to master')
              return { ...prev, currentMode: 'master' }
            })
            // Load the master.md content
            loadMasterContent(firstMasterPath)
          }, 100)
        } else {
          console.log('No master.md found in file tree')
        }
      }
    } catch (err: any) {
      console.error('Error loading file tree:', err)
      setError(err.message || 'Failed to load file tree')
    } finally {
      setLoading(false)
    }
  }

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .file-tree-item.drop-target {
        background-color: rgba(138, 176, 187, 0.2);
        border: 2px dashed #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })
  }

  const selectFile = (path: string) => {
    setSelectedFile(path)

    // If selecting master.md, load its content
    if (path.endsWith('master.md')) {
      loadMasterContent(path)
      setViewMode(prev => ({ ...prev, currentMode: 'master' }))
    }
  }

  const loadMasterContent = async (filePath: string) => {
    try {
      setMasterLoading(true)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.readFile) {
        setMasterContent('# Master Document\n\nContent not available in development mode.')
        return
      }

      const result = await window.electronAPI.vault.readFile(filePath)
      if (result.success && result.content) {
        setMasterContent(result.content)
      } else {
        setMasterContent('# Error\n\nFailed to load master document content.')
      }
    } catch (error) {
      console.error('Error loading master content:', error)
      setMasterContent('# Error\n\nFailed to load master document content.')
    } finally {
      setMasterLoading(false)
    }
  }

  // File drop handlers
  const handleDragOver = (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(folderPath || 'general')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)
  }

  const handleFileDrop = async (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)

    const files = Array.from(e.dataTransfer.files)
    if (files.length === 0) return

    try {
      // TODO: Implement file drop to folder
      console.log(`Dropping ${files.length} files to folder:`, folderPath)

      // For now, just show a message
      alert(`Would drop ${files.length} files to ${folderPath || 'root folder'}`)

      // Refresh file tree after drop
      await loadFileTree()
    } catch (error) {
      console.error('Error dropping files:', error)
    }
  }

  const handleModeChange = (mode: 'explorer' | 'master') => {
    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))

    // Auto-select master.md when switching to master mode
    if (mode === 'master') {
      setSelectedFile('master.md')
    }
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    // Debug: Log expansion check
    if (node.type === 'folder') {
      console.log(`Checking expansion for ${node.name} (${node.path}): ${isExpanded}`)
      console.log('Available expanded paths:', Array.from(expandedFolders))
    }

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all relative
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
            ${dragOver === node.path && node.type === 'folder' ? 'drop-target' : ''}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onDragOver={node.type === 'folder' ? (e) => handleDragOver(e, node.path) : undefined}
          onDragLeave={node.type === 'folder' ? handleDragLeave : undefined}
          onDrop={node.type === 'folder' ? (e) => handleFileDrop(e, node.path) : undefined}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path)
            } else {
              selectFile(node.path)
            }
          }}
        >
          {node.type === 'folder' && (
            <FontAwesomeIcon
              icon={isExpanded ? faChevronDown : faChevronRight}
              className="text-gray-400 text-xs w-3"
            />
          )}
          {node.type === 'file' && <div className="w-3"></div>}

          <FontAwesomeIcon icon={node.icon} className={`text-sm ${node.color}`} />
          
          <span className={`text-sm ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          
          <div className="ml-auto">
            {node.fileCount && (
              <span className={`w-5 h-5 ${node.color === 'text-secondary' ? 'bg-secondary/20 text-secondary' : 'bg-supplement2/20 text-supplement2'} text-xs rounded-full flex items-center justify-center font-medium`}>
                {node.fileCount}
              </span>
            )}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Main Files Content */}
      <div className="flex-1 flex bg-gray-900">
        
        {/* Left Column - File Tree (20%) */}
        <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
          
          {/* File Tree Header */}
          <div className="p-4 border-b border-tertiary/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faFolderTree} className="text-supplement2 text-sm" />
                <h3 className="font-medium text-supplement1 text-sm">Files in Vault</h3>
              </div>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <FontAwesomeIcon icon={faPlus} className="text-gray-400 text-xs" />
                <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  Add File
                </div>
              </button>
            </div>
          </div>

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faSitemap} className="text-sm" />
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master'
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80'
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faLightbulb} className="text-sm" />
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree */}
          <div className="flex-1 overflow-y-auto p-2">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <FontAwesomeIcon icon={faArrowRotateRight} className="text-primary text-lg mb-2 animate-spin" />
                  <p className="text-sm text-supplement1">Loading files...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <p className="text-sm text-secondary mb-2">Error: {error}</p>
                  <button
                    onClick={loadFileTree}
                    className="px-3 py-1 bg-primary text-gray-900 rounded text-xs hover:bg-primary/80 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : (
              fileTree.map(node => renderFileTreeNode(node))
            )}
          </div>
        </div>
        
        {/* Right Column Content */}
        {(() => {
          console.log('Rendering right column, viewMode.currentMode:', viewMode.currentMode)
          console.log('Master content length:', masterContent.length)
          console.log('Master loading:', masterLoading)
          return viewMode.currentMode === 'master' ? (
            <MasterMode
              content={masterContent}
              loading={masterLoading}
              selectedFile={selectedFile}
            />
          ) : (
            <ExplorerMode
              fileTree={fileTree}
              loading={loading}
            />
          )
        })()}
      </div>
    </div>
  )
}

// Master Mode Component
interface MasterModeProps {
  content: string
  loading: boolean
  selectedFile: string | null
}

const MasterMode: React.FC<MasterModeProps> = ({ content, loading, selectedFile }) => {
  // Extract title from content (first # heading)
  const getTitle = (content: string) => {
    const match = content.match(/^#\s+(.+)$/m)
    return match ? match[1] : 'Master Document'
  }

  // Simple markdown to HTML conversion for basic display
  const renderMarkdown = (content: string) => {
    if (!content) return ''

    return content
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold text-supplement1 mb-4">$1</h1>')
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-semibold text-supplement1 mb-3 mt-6">$2</h2>')
      .replace(/^### (.+)$/gm, '<h3 class="text-lg font-medium text-supplement2 mb-2 mt-4">$3</h3>')
      .replace(/^\* (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<strong class="text-supplement1">$1</strong>')
      .replace(/\*(.+?)\*/g, '<em class="text-gray-300">$1</em>')
      .replace(/`(.+?)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-primary text-sm">$1</code>')
      .replace(/\n\n/g, '</p><p class="text-gray-400 mb-4">')
      .replace(/^(?!<[h|l])/gm, '<p class="text-gray-400 mb-4">')
      .replace(/<p class="text-gray-400 mb-4">(<[h|l])/g, '$1')
  }

  return (
    <div className="flex-1 flex">
      {/* Master Document Preview */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <FontAwesomeIcon icon={faArrowRotateRight} className="text-primary text-2xl mb-4 animate-spin" />
              <p className="text-supplement1">Loading master document...</p>
            </div>
          </div>
        ) : (
          <div className="markdown-content">
            {/* Document Header */}
            <div className="flex items-center gap-3 mb-6 pb-4 border-b border-tertiary/30">
              <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faFileText} className="text-primary text-lg" />
              </div>
              <div>
                <h1 className="text-supplement1 text-xl font-semibold">{getTitle(content)}</h1>
                <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                  <span className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faClock} className="text-xs" />
                    Just updated
                  </span>
                  <span className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faFileText} className="text-xs" />
                    {selectedFile ? selectedFile.split('/').pop() : 'master.md'}
                  </span>
                </div>
              </div>
            </div>

            {/* Markdown Content */}
            <div
              className="prose prose-invert max-w-none"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
            />
          </div>
        )}
      </div>

      {/* Artifacts Sidebar will be rendered by ArtifactsSidebar component */}
      {/* <ArtifactsSidebar /> */}
    </div>
  )
}

// Explorer Mode Component
interface ExplorerModeProps {
  fileTree: FileTreeNode[]
  loading: boolean
}

const ExplorerMode: React.FC<ExplorerModeProps> = ({ fileTree, loading }) => {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [sortBy, setSortBy] = useState<'name' | 'modified' | 'type' | 'size'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())

  // If no file tree data, show empty state
  if (fileTree.length === 0 && !loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FontAwesomeIcon icon={faFolder} className="text-supplement1 text-6xl mb-6" />
          <h2 className="text-2xl font-semibold text-supplement1 mb-4">No Context Vaults Found</h2>
          <p className="text-gray-400 mb-8 leading-relaxed">
            You need to set up your context vaults first to organize and manage your files.
            Context vaults help you organize documents, conversations, and AI insights by topic or project.
          </p>
          <button
            onClick={() => window.location.href = '/settings?tab=data'}
            className="px-8 py-4 bg-primary text-gray-900 rounded-lg font-semibold hover:bg-primary/80 transition-colors text-lg"
          >
            Set Up Vaults
          </button>
        </div>
      </div>
    )
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <FontAwesomeIcon icon={faArrowRotateRight} className="text-primary text-3xl mb-4 animate-spin" />
          <p className="text-supplement1 text-lg">Loading vault contents...</p>
        </div>
      </div>
    )
  }

  // Flatten file tree to get all files for details view
  const getAllFiles = (nodes: FileTreeNode[]): FileTreeNode[] => {
    const allFiles: FileTreeNode[] = []

    const traverse = (nodes: FileTreeNode[], parentPath = '') => {
      for (const node of nodes) {
        if (node.type === 'file') {
          allFiles.push({
            ...node,
            path: parentPath ? `${parentPath}/${node.name}` : node.name
          })
        } else if (node.children) {
          // Add folder itself
          allFiles.push({
            ...node,
            path: parentPath ? `${parentPath}/${node.name}` : node.name
          })
          // Traverse children
          traverse(node.children, parentPath ? `${parentPath}/${node.name}` : node.name)
        }
      }
    }

    traverse(nodes)
    return allFiles
  }

  const allFiles = getAllFiles(fileTree)

  // Sort files
  const sortedFiles = [...allFiles].sort((a, b) => {
    let comparison = 0

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'modified':
        comparison = new Date(a.modified || 0).getTime() - new Date(b.modified || 0).getTime()
        break
      case 'type':
        const aType = a.type === 'folder' ? 'Folder' : getFileType(a.name)
        const bType = b.type === 'folder' ? 'Folder' : getFileType(b.name)
        comparison = aType.localeCompare(bType)
        break
      case 'size':
        comparison = (a.size || 0) - (b.size || 0)
        break
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })

  const getFileType = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md': return 'Markdown File'
      case 'pdf': return 'PDF Document'
      case 'txt': return 'Text File'
      case 'docx': case 'doc': return 'Word Document'
      case 'xlsx': case 'xls': return 'Excel Spreadsheet'
      case 'pptx': case 'ppt': return 'PowerPoint Presentation'
      case 'png': case 'jpg': case 'jpeg': case 'gif': return 'Image File'
      case 'json': return 'JSON File'
      default: return 'File'
    }
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '-'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (dateString?: string): string => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)
    const diffWeeks = Math.floor(diffDays / 7)

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    if (diffWeeks < 4) return `${diffWeeks} week${diffWeeks > 1 ? 's' : ''} ago`
    return date.toLocaleDateString()
  }

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const toggleFileSelection = (filePath: string) => {
    const newSelection = new Set(selectedFiles)
    if (newSelection.has(filePath)) {
      newSelection.delete(filePath)
    } else {
      newSelection.add(filePath)
    }
    setSelectedFiles(newSelection)
  }

  const getSortIcon = (column: typeof sortBy) => {
    if (sortBy !== column) return faSort
    return sortOrder === 'asc' ? faSortUp : faSortDown
  }

  return (
    <div className="flex-1 bg-gray-900 flex flex-col">
      {/* Explorer Header */}
      <div className="border-b border-tertiary/50 p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold text-supplement1">File Explorer</h2>
          <span className="text-sm text-gray-400">
            {allFiles.length} item{allFiles.length !== 1 ? 's' : ''}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list'
                ? 'bg-primary/20 text-primary'
                : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
            }`}
          >
            <FontAwesomeIcon icon={faList} className="text-sm" />
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid'
                ? 'bg-primary/20 text-primary'
                : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
            }`}
          >
            <FontAwesomeIcon icon={faThLarge} className="text-sm" />
          </button>
        </div>
      </div>

      {/* File Content */}
      <div className="flex-1 overflow-y-auto">
        {viewMode === 'list' ? (
          <table className="w-full">
            <thead className="bg-gray-800 border-b border-tertiary/50 sticky top-0">
              <tr>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Name
                    <FontAwesomeIcon icon={getSortIcon('name')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('modified')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Date Modified
                    <FontAwesomeIcon icon={getSortIcon('modified')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('type')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Type
                    <FontAwesomeIcon icon={getSortIcon('type')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('size')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Size
                    <FontAwesomeIcon icon={getSortIcon('size')} className="text-xs" />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedFiles.map((file, index) => (
                <tr
                  key={file.path}
                  className={`border-b border-gray-800 cursor-pointer transition-colors hover:bg-gray-800/50 ${
                    selectedFiles.has(file.path) ? 'bg-primary/10 border-l-2 border-l-primary' : ''
                  }`}
                  onClick={() => toggleFileSelection(file.path)}
                >
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <FontAwesomeIcon
                        icon={file.type === 'folder' ? faFolder : faFileLines}
                        className={`text-lg ${
                          file.type === 'folder' ? 'text-supplement2' : 'text-primary'
                        }`}
                      />
                      <span className="text-sm text-supplement1 font-medium">{file.name}</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">{formatDate(file.modified)}</span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">
                      {file.type === 'folder' ? 'Folder' : getFileType(file.name)}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">{formatFileSize(file.size)}</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="p-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {sortedFiles.map((file) => (
              <div
                key={file.path}
                className={`p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border ${
                  selectedFiles.has(file.path)
                    ? 'border-primary/50 bg-primary/10'
                    : 'border-transparent hover:border-primary/30'
                }`}
                onClick={() => toggleFileSelection(file.path)}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-3">
                    <FontAwesomeIcon
                      icon={file.type === 'folder' ? faFolder : faFileLines}
                      className={`text-xl ${
                        file.type === 'folder' ? 'text-supplement2' : 'text-primary'
                      }`}
                    />
                  </div>
                  <h4 className="text-sm font-medium text-supplement1 mb-1 truncate w-full">{file.name}</h4>
                  <p className="text-xs text-gray-400">{formatFileSize(file.size)} • {file.type === 'folder' ? 'Folder' : getFileType(file.name)}</p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(file.modified)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FilesPage
